<Project>

  <!-- 自定义构建目标 -->
  <Target Name="PrintBuildInfo" BeforeTargets="Build">
    <Message Text="Building $(MSBuildProjectName) - $(Configuration) configuration" Importance="high" />
  </Target>

  <!-- 清理自定义输出 -->
  <Target Name="CleanCustomOutput" BeforeTargets="Clean">
    <ItemGroup>
      <CustomOutputFiles Include="$(OutputPath)**\*.pdb" />
      <CustomOutputFiles Include="$(OutputPath)**\*.xml" />
    </ItemGroup>
    <Delete Files="@(CustomOutputFiles)" ContinueOnError="true" />
  </Target>

  <!-- 复制资源文件 -->
  <Target Name="CopyResources" AfterTargets="Build" Condition="Exists('$(MSBuildProjectDirectory)\Resources')">
    <ItemGroup>
      <ResourceFiles Include="$(MSBuildProjectDirectory)\Resources\**\*.*" />
    </ItemGroup>
    <Copy SourceFiles="@(ResourceFiles)" 
          DestinationFiles="@(ResourceFiles->'$(OutputPath)Resources\%(RecursiveDir)%(Filename)%(Extension)')" 
          SkipUnchangedFiles="true" />
  </Target>

  <!-- 生成版本信息文件 -->
  <Target Name="GenerateVersionInfo" BeforeTargets="Build">
    <PropertyGroup>
      <VersionInfoContent>
// 此文件由构建过程自动生成
using System.Reflection;

[assembly: AssemblyMetadata("BuildDate", "$(BuildDate)")]
[assembly: AssemblyMetadata("BuildMachine", "$(COMPUTERNAME)")]
[assembly: AssemblyMetadata("GitCommit", "$(GitCommitId)")]
      </VersionInfoContent>
    </PropertyGroup>
    
    <WriteLinesToFile File="$(IntermediateOutputPath)VersionInfo.cs" 
                      Lines="$(VersionInfoContent)" 
                      Overwrite="true" 
                      Condition="!Exists('$(IntermediateOutputPath)VersionInfo.cs')" />
    
    <ItemGroup>
      <Compile Include="$(IntermediateOutputPath)VersionInfo.cs" />
    </ItemGroup>
  </Target>

  <!-- 包验证 -->
  <Target Name="ValidatePackage" AfterTargets="Pack" Condition="'$(IsPackable)' == 'true'">
    <Message Text="Validating package: $(PackageOutputPath)" Importance="high" />
    <!-- 这里可以添加包验证逻辑 -->
  </Target>

  <!-- 测试项目特定目标 -->
  <Target Name="RunCodeCoverage" AfterTargets="Test" Condition="$(MSBuildProjectName.EndsWith('.Tests'))">
    <Message Text="Generating code coverage report for $(MSBuildProjectName)" Importance="high" />
    <!-- 代码覆盖率报告生成逻辑 -->
  </Target>

  <!-- 文档生成 -->
  <Target Name="GenerateDocumentation" AfterTargets="Build" Condition="'$(Configuration)' == 'Release' AND '$(GenerateDocumentationFile)' == 'true'">
    <Message Text="Documentation file generated: $(DocumentationFile)" Importance="high" />
  </Target>

  <!-- 设置构建日期 -->
  <PropertyGroup>
    <BuildDate>$([System.DateTime]::Now.ToString("yyyy-MM-dd HH:mm:ss"))</BuildDate>
  </PropertyGroup>

</Project>
