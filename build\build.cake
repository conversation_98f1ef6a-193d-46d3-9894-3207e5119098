// Cake Build Script for FluentDesign.WPF

#tool nuget:?package=MSBuild.SonarQube.Runner.Tool&version=4.8.0
#addin nuget:?package=Cake.Sonar&version=1.1.25

var target = Argument("target", "Default");
var configuration = Argument("configuration", "Release");
var solution = "./FluentDesign.WPF.sln";
var artifactsDir = "./artifacts";
var testResultsDir = "./test-results";

Task("Clean")
    .Does(() =>
{
    CleanDirectory(artifactsDir);
    CleanDirectory(testResultsDir);
    CleanDirectories("./src/**/bin");
    CleanDirectories("./src/**/obj");
    CleanDirectories("./tests/**/bin");
    CleanDirectories("./tests/**/obj");
});

Task("Restore")
    .IsDependentOn("Clean")
    .Does(() =>
{
    DotNetRestore(solution);
});

Task("Build")
    .IsDependentOn("Restore")
    .Does(() =>
{
    var settings = new DotNetBuildSettings
    {
        Configuration = configuration,
        NoRestore = true,
        MSBuildSettings = new DotNetMSBuildSettings()
    };
    
    DotNetBuild(solution, settings);
});

Task("Test")
    .IsDependentOn("Build")
    .Does(() =>
{
    var projects = GetFiles("./tests/**/*.Tests.csproj");
    
    foreach(var project in projects)
    {
        var settings = new DotNetTestSettings
        {
            Configuration = configuration,
            NoBuild = true,
            ResultsDirectory = testResultsDir,
            Loggers = new[] { "trx", "console;verbosity=normal" },
            Collectors = new[] { "XPlat Code Coverage" }
        };
        
        DotNetTest(project.FullPath, settings);
    }
});

Task("Pack")
    .IsDependentOn("Test")
    .Does(() =>
{
    var settings = new DotNetPackSettings
    {
        Configuration = configuration,
        OutputDirectory = artifactsDir,
        NoBuild = true,
        IncludeSymbols = true,
        SymbolPackageFormat = "snupkg"
    };
    
    DotNetPack("./src/FluentDesign.WPF/FluentDesign.WPF.csproj", settings);
});

Task("SonarBegin")
    .Does(() =>
{
    SonarBegin(new SonarBeginSettings
    {
        Key = "FluentDesign.WPF",
        Name = "FluentDesign WPF Controls",
        Version = "1.0.0",
        Url = "https://sonarcloud.io",
        Login = EnvironmentVariable("SONAR_TOKEN"),
        Organization = "fluentdesign-wpf"
    });
});

Task("SonarEnd")
    .Does(() =>
{
    SonarEnd(new SonarEndSettings
    {
        Login = EnvironmentVariable("SONAR_TOKEN")
    });
});

Task("CodeAnalysis")
    .IsDependentOn("SonarBegin")
    .IsDependentOn("Build")
    .IsDependentOn("Test")
    .IsDependentOn("SonarEnd");

Task("Publish")
    .IsDependentOn("Pack")
    .Does(() =>
{
    var apiKey = EnvironmentVariable("NUGET_API_KEY");
    var source = EnvironmentVariable("NUGET_SOURCE") ?? "https://api.nuget.org/v3/index.json";
    
    if (string.IsNullOrEmpty(apiKey))
    {
        Warning("NuGet API key not provided. Skipping publish.");
        return;
    }
    
    var packages = GetFiles($"{artifactsDir}/*.nupkg");
    
    foreach(var package in packages)
    {
        DotNetNuGetPush(package.FullPath, new DotNetNuGetPushSettings
        {
            Source = source,
            ApiKey = apiKey
        });
    }
});

Task("Default")
    .IsDependentOn("Pack");

Task("CI")
    .IsDependentOn("CodeAnalysis")
    .IsDependentOn("Pack");

Task("Release")
    .IsDependentOn("CI")
    .IsDependentOn("Publish");

RunTarget(target);
