Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{5798A3EF-**************-27013DD36DD2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{029EB9BE-5563-4B60-9EFF-AD4599868DF1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "build", "build", "{DF46616E-DF7C-4F37-8700-E091FF11EB7F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{A9F3FAA4-7EDB-44AA-B66E-9F52AA963D2B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{B3658CC6-EB9A-46ED-95F3-D1C8E03BF660}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.gitignore = .gitignore
		Directory.Build.props = Directory.Build.props
		Directory.Build.targets = Directory.Build.targets
		README.md = README.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentDesign.WPF", "src\FluentDesign.WPF\FluentDesign.WPF.csproj", "{11111111-2222-3333-4444-555555555555}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentDesign.Demo", "src\FluentDesign.Demo\FluentDesign.Demo.csproj", "{22222222-3333-4444-5555-666666666666}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentDesign.Shared", "src\FluentDesign.Shared\FluentDesign.Shared.csproj", "{*************-5555-6666-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentDesign.Tools", "src\FluentDesign.Tools\FluentDesign.Tools.csproj", "{*************-6666-7777-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentDesign.WPF.Tests", "tests\FluentDesign.WPF.Tests\FluentDesign.WPF.Tests.csproj", "{*************-7777-8888-************}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentDesign.WPF.UITests", "tests\FluentDesign.WPF.UITests\FluentDesign.WPF.UITests.csproj", "{*************-8888-9999-AAAAAAAAAAAA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FluentDesign.WPF.StressTests", "tests\FluentDesign.WPF.StressTests\FluentDesign.WPF.StressTests.csproj", "{*************-9999-AAAA-BBBBBBBBBBBB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{11111111-2222-3333-4444-555555555555}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11111111-2222-3333-4444-555555555555}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11111111-2222-3333-4444-555555555555}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11111111-2222-3333-4444-555555555555}.Release|Any CPU.Build.0 = Release|Any CPU
		{22222222-3333-4444-5555-666666666666}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22222222-3333-4444-5555-666666666666}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22222222-3333-4444-5555-666666666666}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22222222-3333-4444-5555-666666666666}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-5555-6666-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-5555-6666-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-5555-6666-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-5555-6666-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-6666-7777-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-6666-7777-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-6666-7777-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-6666-7777-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-7777-8888-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-7777-8888-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-7777-8888-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-7777-8888-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-8888-9999-AAAAAAAAAAAA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-8888-9999-AAAAAAAAAAAA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-8888-9999-AAAAAAAAAAAA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-8888-9999-AAAAAAAAAAAA}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-9999-AAAA-BBBBBBBBBBBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-9999-AAAA-BBBBBBBBBBBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-9999-AAAA-BBBBBBBBBBBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-9999-AAAA-BBBBBBBBBBBB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{11111111-2222-3333-4444-555555555555} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{22222222-3333-4444-5555-666666666666} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{*************-5555-6666-************} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{*************-6666-7777-************} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{*************-7777-8888-************} = {5798A3EF-**************-27013DD36DD2}
		{*************-8888-9999-AAAAAAAAAAAA} = {5798A3EF-**************-27013DD36DD2}
		{*************-9999-AAAA-BBBBBBBBBBBB} = {5798A3EF-**************-27013DD36DD2}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-9ABC-DEF0-1234-56789ABCDEF0}
	EndGlobalSection
EndGlobal
