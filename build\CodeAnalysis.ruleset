<?xml version="1.0" encoding="utf-8"?>
<RuleSet Name="FluentDesign.WPF Code Analysis Rules" Description="Code analysis rules for FluentDesign.WPF projects" ToolsVersion="16.0">
  <Localization ResourceAssembly="Microsoft.VisualStudio.CodeAnalysis.RuleSets.Strings.dll" ResourceBaseName="Microsoft.VisualStudio.CodeAnalysis.RuleSets.Strings.Localized">
    <Name Resource="FluentDesign_WPF_Title" />
    <Description Resource="FluentDesign_WPF_Description" />
  </Localization>
  
  <!-- Microsoft Design Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="CA1001" Action="Warning" />
    <Rule Id="CA1009" Action="Warning" />
    <Rule Id="CA1016" Action="Warning" />
    <Rule Id="CA1033" Action="Warning" />
    <Rule Id="CA1049" Action="Warning" />
    <Rule Id="CA1060" Action="Warning" />
    <Rule Id="CA1061" Action="Warning" />
    <Rule Id="CA1063" Action="Warning" />
    <Rule Id="CA1065" Action="Warning" />
    <Rule Id="CA1301" Action="Warning" />
    <Rule Id="CA1400" Action="Warning" />
    <Rule Id="CA1401" Action="Warning" />
    <Rule Id="CA1403" Action="Warning" />
    <Rule Id="CA1404" Action="Warning" />
    <Rule Id="CA1405" Action="Warning" />
    <Rule Id="CA1410" Action="Warning" />
    <Rule Id="CA1415" Action="Warning" />
    <Rule Id="CA1821" Action="Warning" />
    <Rule Id="CA1900" Action="Warning" />
    <Rule Id="CA1901" Action="Warning" />
    <Rule Id="CA2002" Action="Warning" />
    <Rule Id="CA2100" Action="Warning" />
    <Rule Id="CA2101" Action="Warning" />
    <Rule Id="CA2108" Action="Warning" />
    <Rule Id="CA2111" Action="Warning" />
    <Rule Id="CA2112" Action="Warning" />
    <Rule Id="CA2114" Action="Warning" />
    <Rule Id="CA2116" Action="Warning" />
    <Rule Id="CA2117" Action="Warning" />
    <Rule Id="CA2122" Action="Warning" />
    <Rule Id="CA2123" Action="Warning" />
    <Rule Id="CA2124" Action="Warning" />
    <Rule Id="CA2126" Action="Warning" />
    <Rule Id="CA2131" Action="Warning" />
    <Rule Id="CA2132" Action="Warning" />
    <Rule Id="CA2133" Action="Warning" />
    <Rule Id="CA2134" Action="Warning" />
    <Rule Id="CA2137" Action="Warning" />
    <Rule Id="CA2138" Action="Warning" />
    <Rule Id="CA2140" Action="Warning" />
    <Rule Id="CA2141" Action="Warning" />
    <Rule Id="CA2146" Action="Warning" />
    <Rule Id="CA2147" Action="Warning" />
    <Rule Id="CA2149" Action="Warning" />
    <Rule Id="CA2200" Action="Warning" />
    <Rule Id="CA2202" Action="Warning" />
    <Rule Id="CA2207" Action="Warning" />
    <Rule Id="CA2212" Action="Warning" />
    <Rule Id="CA2213" Action="Warning" />
    <Rule Id="CA2214" Action="Warning" />
    <Rule Id="CA2216" Action="Warning" />
    <Rule Id="CA2220" Action="Warning" />
    <Rule Id="CA2229" Action="Warning" />
    <Rule Id="CA2231" Action="Warning" />
    <Rule Id="CA2232" Action="Warning" />
    <Rule Id="CA2235" Action="Warning" />
    <Rule Id="CA2236" Action="Warning" />
    <Rule Id="CA2237" Action="Warning" />
    <Rule Id="CA2238" Action="Warning" />
    <Rule Id="CA2240" Action="Warning" />
    <Rule Id="CA2241" Action="Warning" />
    <Rule Id="CA2242" Action="Warning" />
  </Rules>
  
  <!-- StyleCop Rules -->
  <Rules AnalyzerId="StyleCop.Analyzers" RuleNamespace="StyleCop.Analyzers">
    <Rule Id="SA1633" Action="None" /> <!-- File header -->
    <Rule Id="SA1200" Action="None" /> <!-- Using directives placement -->
    <Rule Id="SA1400" Action="Warning" /> <!-- Access modifier must be declared -->
    <Rule Id="SA1600" Action="Info" /> <!-- Elements must be documented -->
    <Rule Id="SA1601" Action="None" /> <!-- Partial elements must be documented -->
    <Rule Id="SA1602" Action="None" /> <!-- Enumeration items must be documented -->
  </Rules>
  
  <!-- Custom WPF Rules -->
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <!-- Dependency Property Rules -->
    <Rule Id="WPF0001" Action="Warning" /> <!-- Backing field for a DependencyProperty should match registered name -->
    <Rule Id="WPF0002" Action="Warning" /> <!-- Backing field for a DependencyProperty should be static and readonly -->
    <Rule Id="WPF0003" Action="Warning" /> <!-- CLR property for a DependencyProperty should use GetValue and SetValue -->
    <Rule Id="WPF0004" Action="Warning" /> <!-- CLR method for a DependencyProperty should use GetValue and SetValue -->
    <Rule Id="WPF0005" Action="Warning" /> <!-- Name of PropertyChangedCallback should match registered name -->
    <Rule Id="WPF0006" Action="Warning" /> <!-- CoerceValueCallback should match registered name -->
    <Rule Id="WPF0007" Action="Warning" /> <!-- Name of ValidateValueCallback should match registered name -->
  </Rules>
</RuleSet>
