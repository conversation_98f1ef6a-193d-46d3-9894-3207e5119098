# 开发指南

FluentDesign.WPF 控件库的开发指南，包含项目架构、开发规范和最佳实践。

## 目录

### 🏗️ 项目架构
- [项目结构](project-structure.md) - 解决方案和项目组织
- [设计原则](design-principles.md) - 架构设计原则和模式
- [依赖关系](dependencies.md) - 项目间依赖和外部依赖

### 🛠️ 开发环境
- [环境搭建](development-setup.md) - 开发环境配置
- [构建和测试](build-and-test.md) - 构建流程和测试策略
- [调试技巧](debugging.md) - 调试和故障排除

### 📝 代码规范
- [编码标准](coding-standards.md) - C# 和 XAML 编码规范
- [命名约定](naming-conventions.md) - 命名规则和约定
- [代码审查](code-review.md) - 代码审查流程和检查清单

### 🎨 控件开发
- [自定义控件开发](custom-controls.md) - 创建自定义控件
- [样式和模板](styles-and-templates.md) - 样式和控件模板开发
- [主题系统](theme-system.md) - 主题系统架构和扩展

### 🧪 测试指南
- [单元测试](unit-testing.md) - 单元测试编写和运行
- [UI 自动化测试](ui-testing.md) - UI 自动化测试
- [性能测试](performance-testing.md) - 性能和压力测试

### 📦 发布流程
- [版本管理](versioning.md) - 版本号管理和发布策略
- [包发布](package-publishing.md) - NuGet 包发布流程
- [文档更新](documentation.md) - 文档维护和更新

### 🤝 贡献指南
- [如何贡献](contributing.md) - 贡献代码的流程和规范
- [问题报告](issue-reporting.md) - 如何报告问题和建议
- [社区规范](community-guidelines.md) - 社区行为准则

## 快速开始

### 1. 克隆仓库
```bash
git clone https://github.com/FluentDesign/FluentDesign.WPF.git
cd FluentDesign.WPF
```

### 2. 安装依赖
```bash
dotnet restore
```

### 3. 构建项目
```bash
dotnet build
```

### 4. 运行测试
```bash
dotnet test
```

### 5. 启动演示应用
```bash
dotnet run --project src/FluentDesign.Demo
```

## 开发工作流

### 功能开发流程
1. 创建功能分支
2. 实现功能代码
3. 编写单元测试
4. 更新文档
5. 提交 Pull Request
6. 代码审查
7. 合并到主分支

### 发布流程
1. 更新版本号
2. 运行完整测试套件
3. 生成发布说明
4. 创建 Release Tag
5. 发布 NuGet 包
6. 更新文档网站

## 工具和资源

### 开发工具
- **IDE**: Visual Studio 2022 或 Visual Studio Code
- **版本控制**: Git
- **包管理**: NuGet
- **构建工具**: MSBuild, Cake
- **CI/CD**: Azure DevOps

### 有用的资源
- [Microsoft Fluent Design System](https://www.microsoft.com/design/fluent/)
- [WPF 官方文档](https://docs.microsoft.com/en-us/dotnet/desktop/wpf/)
- [.NET API 浏览器](https://docs.microsoft.com/en-us/dotnet/api/)

## 常见问题

### Q: 如何添加新的控件？
A: 请参考[自定义控件开发](custom-controls.md)指南。

### Q: 如何修改现有控件的样式？
A: 请参考[样式和模板](styles-and-templates.md)指南。

### Q: 如何报告 Bug？
A: 请参考[问题报告](issue-reporting.md)指南。

---

💡 **提示**: 如果您是第一次参与开源项目，建议先阅读[贡献指南](contributing.md)了解基本流程。
