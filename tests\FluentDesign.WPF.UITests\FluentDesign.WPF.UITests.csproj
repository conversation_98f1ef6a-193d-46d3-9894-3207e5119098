<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <AssemblyTitle>FluentDesign WPF UI Tests</AssemblyTitle>
    <AssemblyDescription>UI automation tests for FluentDesign WPF Controls</AssemblyDescription>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.9.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.9.3" />
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="FluentAssertions" Version="8.3.0" />
    <PackageReference Include="Appium.WebDriver" Version="8.0.0" />
    <PackageReference Include="Microsoft.Windows.SDK.Contracts" Version="10.0.26100.4188" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\FluentDesign.WPF\FluentDesign.WPF.csproj" />
    <ProjectReference Include="..\..\src\FluentDesign.Demo\FluentDesign.Demo.csproj" />
    <ProjectReference Include="..\..\src\FluentDesign.Shared\FluentDesign.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="PageObjects\" />
    <Folder Include="TestCases\" />
    <Folder Include="Helpers\" />
    <Folder Include="TestData\" />
    <Folder Include="Screenshots\" />
  </ItemGroup>

</Project>
