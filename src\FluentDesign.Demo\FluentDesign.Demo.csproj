<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>Resources\Icons\app.ico</ApplicationIcon>
    <AssemblyTitle>FluentDesign WPF Demo</AssemblyTitle>
    <AssemblyDescription>FluentDesign WPF Controls Demo and Browser Application</AssemblyDescription>
    <StartupObject>FluentDesign.Demo.App</StartupObject>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FluentDesign.WPF\FluentDesign.WPF.csproj" />
    <ProjectReference Include="..\FluentDesign.Shared\FluentDesign.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*.*" />
  </ItemGroup>

  <ItemGroup>
    <Page Include="App\App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="App\MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>

  <ItemGroup>
    <Compile Include="App\App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="App\MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Pages\Home\" />
    <Folder Include="Pages\Basic\" />
    <Folder Include="Pages\Navigation\" />
    <Folder Include="Pages\Layout\" />
    <Folder Include="Pages\Data\" />
    <Folder Include="Pages\Settings\" />
    <Folder Include="ViewModels\Controls\" />
    <Folder Include="Models\" />
    <Folder Include="Services\ThemeService\" />
    <Folder Include="Services\NavigationService\" />
    <Folder Include="Services\CodeGenerationService\" />
    <Folder Include="Resources\Images\" />
    <Folder Include="Resources\Icons\" />
    <Folder Include="Helpers\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Resources\Icons\app.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
