# FluentDesign.WPF

一个基于 Fluent Design System 的现代化 WPF 控件库。

## 项目概述

FluentDesign.WPF 是一个完整的 WPF 控件库解决方案，旨在为 WPF 应用程序提供符合 Microsoft Fluent Design 设计语言的现代化、美观且高性能的控件集合。

### 主要特性

- 🎨 **Fluent Design** - 完全遵循 Microsoft Fluent Design System 设计规范
- 🌙 **主题支持** - 内置亮色和暗色主题，支持系统主题自动切换
- ⚡ **高性能** - 优化的渲染性能，支持虚拟化和大数据量场景
- 🧩 **模块化** - 灵活的控件组合和自定义样式支持
- 📱 **响应式** - 支持不同屏幕尺寸和 DPI 设置
- 🔧 **易于使用** - 简洁的 API 设计和丰富的文档支持

### 项目结构

```
FluentDesign.WPF/
├── src/                            # 源代码目录
│   ├── FluentDesign.WPF/           # 核心控件库
│   ├── FluentDesign.Demo/          # 控件展示和浏览应用
│   ├── FluentDesign.Shared/        # 共享代码和资源
│   └── FluentDesign.Tools/         # 开发工具
├── tests/                          # 测试项目
├── docs/                           # 文档
├── build/                          # 构建脚本和配置
├── tools/                          # 开发工具
└── assets/                         # 设计资源
```

## 快速开始

### 系统要求

- .NET 8.0 或更高版本
- Windows 10 版本 1809 或更高版本
- Visual Studio 2022 或 Visual Studio Code

### 安装

通过 NuGet 包管理器安装：

```powershell
Install-Package FluentDesign.WPF
```

或通过 .NET CLI：

```bash
dotnet add package FluentDesign.WPF
```

### 基本使用

1. 在 App.xaml 中引用主题资源：

```xml
<Application x:Class="YourApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FluentDesign.WPF;component/Themes/Generic.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
```

2. 在 XAML 中使用控件：

```xml
<Window x:Class="YourApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:fluent="clr-namespace:FluentDesign.WPF.Controls;assembly=FluentDesign.WPF">
    <Grid>
        <fluent:FluentButton Content="Hello Fluent Design!" 
                            Style="{StaticResource AccentButtonStyle}" />
    </Grid>
</Window>
```

## 控件库

### 基础控件
- FluentButton - 现代化按钮控件
- FluentTextBox - 输入框控件
- FluentComboBox - 下拉选择框
- FluentCheckBox - 复选框
- FluentRadioButton - 单选按钮

### 导航控件
- FluentNavigationView - 导航视图
- FluentTabView - 标签页控件
- FluentBreadcrumb - 面包屑导航

### 布局控件
- FluentCard - 卡片容器
- FluentExpander - 可展开容器
- FluentSplitView - 分割视图

### 数据控件
- FluentDataGrid - 数据网格
- FluentListView - 列表视图
- FluentTreeView - 树形视图

## 开发指南

### 构建项目

```bash
# 克隆仓库
git clone https://github.com/FluentDesign/FluentDesign.WPF.git
cd FluentDesign.WPF

# 还原 NuGet 包
dotnet restore

# 构建解决方案
dotnet build

# 运行演示应用
dotnet run --project src/FluentDesign.Demo
```

### 运行测试

```bash
# 运行所有测试
dotnet test

# 运行特定测试项目
dotnet test tests/FluentDesign.WPF.Tests
```

## 文档

- [API 文档](docs/api/README.md)
- [开发指南](docs/guides/README.md)
- [设计规范](docs/design/README.md)
- [示例教程](docs/tutorials/README.md)

## 贡献

我们欢迎社区贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- Microsoft Fluent Design System
- WPF 社区
- 所有贡献者

## 联系我们

- 项目主页：https://github.com/FluentDesign/FluentDesign.WPF
- 问题反馈：https://github.com/FluentDesign/FluentDesign.WPF/issues
- 讨论区：https://github.com/FluentDesign/FluentDesign.WPF/discussions

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！
