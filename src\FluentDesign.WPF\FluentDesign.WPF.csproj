<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>FluentDesign.WPF</PackageId>
    <Title>FluentDesign WPF Controls</Title>
    <Description>A modern WPF control library based on Microsoft Fluent Design System</Description>
    <PackageTags>WPF;Fluent;Design;Controls;UI;Windows;Microsoft</PackageTags>
    <RepositoryUrl>https://github.com/FluentDesign/FluentDesign.WPF</RepositoryUrl>
    <PackageProjectUrl>https://github.com/FluentDesign/FluentDesign.WPF</PackageProjectUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageIcon>icon.png</PackageIcon>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FluentDesign.Shared\FluentDesign.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\README.md" Pack="true" PackagePath="\" />
    <None Include="..\..\assets\icon.png" Pack="true" PackagePath="\" />
  </ItemGroup>

  <ItemGroup>
    <Page Include="Themes\Generic.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Themes\**\*.xaml" Exclude="Themes\Generic.xaml" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\AssemblyInfo.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AssemblyInfo.tt</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controls\Basic\" />
    <Folder Include="Controls\Navigation\" />
    <Folder Include="Controls\Layout\" />
    <Folder Include="Controls\Data\" />
    <Folder Include="Controls\Dialog\" />
    <Folder Include="Controls\Media\" />
    <Folder Include="Themes\Dark\" />
    <Folder Include="Themes\Light\" />
    <Folder Include="Animations\" />
    <Folder Include="Brushes\" />
    <Folder Include="Effects\" />
    <Folder Include="Converters\" />
    <Folder Include="Attached\" />
    <Folder Include="Extensions\" />
    <Folder Include="Behaviors\" />
    <Folder Include="Helpers\" />
  </ItemGroup>

</Project>
