{"Version": 1, "WorkspaceRootPath": "D:\\02 FluentDesign.WPF\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{11111111-2222-3333-4444-555555555555}|src\\FluentDesign.WPF\\FluentDesign.WPF.csproj|d:\\02 fluentdesign.wpf\\src\\fluentdesign.wpf\\fluentdesign.wpf.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{11111111-2222-3333-4444-555555555555}|src\\FluentDesign.WPF\\FluentDesign.WPF.csproj|solutionrelative:src\\fluentdesign.wpf\\fluentdesign.wpf.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{44444444-5555-6666-7777-888888888888}|src\\FluentDesign.Tools\\FluentDesign.Tools.csproj|d:\\02 fluentdesign.wpf\\src\\fluentdesign.tools\\fluentdesign.tools.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{44444444-5555-6666-7777-888888888888}|src\\FluentDesign.Tools\\FluentDesign.Tools.csproj|solutionrelative:src\\fluentdesign.tools\\fluentdesign.tools.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{44444444-5555-6666-7777-888888888888}|src\\FluentDesign.Tools\\FluentDesign.Tools.csproj|d:\\02 fluentdesign.wpf\\src\\fluentdesign.tools\\fluentdesign.tools.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{44444444-5555-6666-7777-888888888888}|src\\FluentDesign.Tools\\FluentDesign.Tools.csproj|solutionrelative:src\\fluentdesign.tools\\fluentdesign.tools.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{33333333-4444-5555-6666-777777777777}|src\\FluentDesign.Shared\\FluentDesign.Shared.csproj|d:\\02 fluentdesign.wpf\\src\\fluentdesign.shared\\fluentdesign.shared.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{33333333-4444-5555-6666-777777777777}|src\\FluentDesign.Shared\\FluentDesign.Shared.csproj|solutionrelative:src\\fluentdesign.shared\\fluentdesign.shared.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\dotnet\\sdk\\9.0.300\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.Sdk.DefaultItems.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "FluentDesign.WPF", "DocumentMoniker": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.WPF\\FluentDesign.WPF.csproj", "RelativeDocumentMoniker": "src\\FluentDesign.WPF\\FluentDesign.WPF.csproj", "ToolTip": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.WPF\\FluentDesign.WPF.csproj", "RelativeToolTip": "src\\FluentDesign.WPF\\FluentDesign.WPF.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-23T06:29:31.164Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "FluentDesign.Tools", "DocumentMoniker": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Tools\\FluentDesign.Tools.csproj", "RelativeDocumentMoniker": "src\\FluentDesign.Tools\\FluentDesign.Tools.csproj", "ToolTip": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Tools\\FluentDesign.Tools.csproj", "RelativeToolTip": "src\\FluentDesign.Tools\\FluentDesign.Tools.csproj", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-23T06:29:15.958Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "FluentDesign.Tools", "DocumentMoniker": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Tools\\FluentDesign.Tools.csproj", "RelativeDocumentMoniker": "src\\FluentDesign.Tools\\FluentDesign.Tools.csproj", "ToolTip": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Tools\\FluentDesign.Tools.csproj", "RelativeToolTip": "src\\FluentDesign.Tools\\FluentDesign.Tools.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-23T06:29:19.971Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "FluentDesign.Shared", "DocumentMoniker": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Shared\\FluentDesign.Shared.csproj", "RelativeDocumentMoniker": "src\\FluentDesign.Shared\\FluentDesign.Shared.csproj", "ToolTip": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Shared\\FluentDesign.Shared.csproj", "RelativeToolTip": "src\\FluentDesign.Shared\\FluentDesign.Shared.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-23T06:29:07.202Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Microsoft.NET.Sdk.DefaultItems.targets", "DocumentMoniker": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.Sdk.DefaultItems.targets", "ToolTip": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.Sdk.DefaultItems.targets", "ViewState": "AgIAAHsAAAAAAAAAAAAnwIwAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-06-23T06:29:00.383Z", "EditorCaption": ""}]}]}]}