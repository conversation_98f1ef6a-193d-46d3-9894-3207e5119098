<Project>

  <!-- 版本信息 -->
  <PropertyGroup>
    <VersionPrefix>1.0.0</VersionPrefix>
    <VersionSuffix Condition="'$(Configuration)' == 'Debug'">preview</VersionSuffix>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
  </PropertyGroup>

  <!-- 目标框架 -->
  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <!-- 编译配置 -->
  <PropertyGroup>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
    <NoWarn>$(NoWarn);CS1591</NoWarn> <!-- 缺少XML文档注释 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <!-- 包信息 -->
  <PropertyGroup>
    <Company>FluentDesign WPF Team</Company>
    <Product>FluentDesign WPF Controls</Product>
    <Copyright>Copyright © FluentDesign WPF Team 2024</Copyright>
    <Description>A modern WPF control library based on Fluent Design System</Description>
    <PackageProjectUrl>https://github.com/FluentDesign/FluentDesign.WPF</PackageProjectUrl>
    <RepositoryUrl>https://github.com/FluentDesign/FluentDesign.WPF</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageTags>WPF;Fluent;Design;Controls;UI;Windows</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <Authors>FluentDesign WPF Team</Authors>
    <Owners>FluentDesign WPF Team</Owners>
  </PropertyGroup>

  <!-- 输出配置 -->
  <PropertyGroup>
    <OutputPath>$(MSBuildThisFileDirectory)bin\$(Configuration)\</OutputPath>
    <BaseIntermediateOutputPath>$(MSBuildThisFileDirectory)obj\</BaseIntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>true</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
  </PropertyGroup>

  <!-- 调试配置 -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <!-- 发布配置 -->
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- 代码分析 -->
  <PropertyGroup>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <CodeAnalysisRuleSet>$(MSBuildThisFileDirectory)build\CodeAnalysis.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <!-- 源链接配置 -->
  <PropertyGroup>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>

  <!-- 全局包引用 -->
  <ItemGroup>
    <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All"/>
  </ItemGroup>

  <!-- 测试项目特定配置 -->
  <PropertyGroup Condition="$(MSBuildProjectName.EndsWith('.Tests'))">
    <IsPackable>false</IsPackable>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
  </PropertyGroup>

  <!-- 工具项目特定配置 -->
  <PropertyGroup Condition="$(MSBuildProjectName.EndsWith('.Tools'))">
    <IsPackable>false</IsPackable>
    <OutputType>Exe</OutputType>
  </PropertyGroup>

  <!-- Demo项目特定配置 -->
  <PropertyGroup Condition="$(MSBuildProjectName.EndsWith('.Demo'))">
    <IsPackable>false</IsPackable>
    <OutputType>WinExe</OutputType>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

</Project>
