# FluentDesign.WPF 文档

欢迎来到 FluentDesign.WPF 控件库的文档中心。这里包含了使用和开发该控件库所需的所有信息。

## 文档结构

### 📚 [API 文档](api/README.md)
- 完整的 API 参考文档
- 控件属性、方法和事件说明
- 代码示例和使用说明

### 📖 [开发指南](guides/README.md)
- 项目架构和设计原则
- 开发环境搭建
- 代码规范和最佳实践
- 贡献指南

### 🎓 [教程](tutorials/README.md)
- 快速入门教程
- 控件使用示例
- 高级功能教程
- 常见问题解答

### 🎨 [设计规范](design/README.md)
- Fluent Design 设计原则
- 视觉设计指南
- 主题和样式规范
- 图标和资源规范

## 快速导航

### 新手入门
1. [安装和配置](tutorials/getting-started.md)
2. [第一个应用](tutorials/first-app.md)
3. [基础控件使用](tutorials/basic-controls.md)

### 开发者
1. [项目结构](guides/project-structure.md)
2. [构建和测试](guides/build-and-test.md)
3. [自定义控件开发](guides/custom-controls.md)

### 设计师
1. [设计系统概述](design/design-system.md)
2. [颜色和主题](design/colors-and-themes.md)
3. [图标和资源](design/icons-and-resources.md)

## 版本信息

- **当前版本**: 1.0.0
- **最低要求**: .NET 8.0, Windows 10 1809+
- **支持的 Visual Studio 版本**: 2022 17.0+

## 获取帮助

- 🐛 [报告问题](https://github.com/FluentDesign/FluentDesign.WPF/issues)
- 💬 [讨论区](https://github.com/FluentDesign/FluentDesign.WPF/discussions)
- 📧 [联系我们](mailto:<EMAIL>)

## 贡献

我们欢迎社区贡献！请查看我们的[贡献指南](guides/contributing.md)了解如何参与项目开发。

---

📝 **注意**: 文档持续更新中，如发现任何问题或有改进建议，请通过 GitHub Issues 反馈。
