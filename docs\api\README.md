# API 文档

FluentDesign.WPF 控件库的完整 API 参考文档。

## 命名空间

### FluentDesign.WPF.Controls
包含所有用户界面控件的核心命名空间。

#### 基础控件
- [FluentButton](controls/FluentButton.md) - 现代化按钮控件
- [FluentTextBox](controls/FluentTextBox.md) - 输入框控件
- [FluentComboBox](controls/FluentComboBox.md) - 下拉选择框
- [FluentCheckBox](controls/FluentCheckBox.md) - 复选框控件
- [FluentRadioButton](controls/FluentRadioButton.md) - 单选按钮

#### 导航控件
- [FluentNavigationView](controls/FluentNavigationView.md) - 导航视图
- [FluentTabView](controls/FluentTabView.md) - 标签页控件
- [FluentBreadcrumb](controls/FluentBreadcrumb.md) - 面包屑导航

#### 布局控件
- [FluentCard](controls/FluentCard.md) - 卡片容器
- [FluentExpander](controls/FluentExpander.md) - 可展开容器
- [FluentSplitView](controls/FluentSplitView.md) - 分割视图

#### 数据控件
- [FluentDataGrid](controls/FluentDataGrid.md) - 数据网格
- [FluentListView](controls/FluentListView.md) - 列表视图
- [FluentTreeView](controls/FluentTreeView.md) - 树形视图

### FluentDesign.WPF.Themes
主题和样式相关的类和资源。

- [ThemeManager](themes/ThemeManager.md) - 主题管理器
- [FluentColors](themes/FluentColors.md) - Fluent 颜色定义
- [FluentBrushes](themes/FluentBrushes.md) - Fluent 画刷资源

### FluentDesign.WPF.Animations
动画效果相关的类和资源。

- [FluentAnimations](animations/FluentAnimations.md) - 动画效果集合
- [TransitionHelper](animations/TransitionHelper.md) - 过渡动画辅助类

### FluentDesign.WPF.Converters
值转换器集合。

- [BooleanToVisibilityConverter](converters/BooleanToVisibilityConverter.md)
- [ColorToBrushConverter](converters/ColorToBrushConverter.md)
- [ThicknessConverter](converters/ThicknessConverter.md)

### FluentDesign.WPF.Attached
附加属性集合。

- [FluentProperties](attached/FluentProperties.md) - Fluent 附加属性
- [AnimationProperties](attached/AnimationProperties.md) - 动画附加属性

### FluentDesign.WPF.Behaviors
行为集合。

- [FocusBehavior](behaviors/FocusBehavior.md) - 焦点行为
- [HoverBehavior](behaviors/HoverBehavior.md) - 悬停行为

## 使用示例

### 基本用法

```xml
<Window x:Class="MyApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:fluent="clr-namespace:FluentDesign.WPF.Controls;assembly=FluentDesign.WPF">
    <Grid>
        <fluent:FluentButton Content="Click Me" 
                            Style="{StaticResource AccentButtonStyle}" />
    </Grid>
</Window>
```

### 主题切换

```csharp
using FluentDesign.WPF.Themes;

// 切换到暗色主题
ThemeManager.Current.ApplicationTheme = ApplicationTheme.Dark;

// 切换到亮色主题
ThemeManager.Current.ApplicationTheme = ApplicationTheme.Light;
```

## 版本历史

### v1.0.0
- 初始版本发布
- 包含基础控件集合
- 支持亮色和暗色主题

---

📝 **注意**: API 文档会随着版本更新而变化，请确保查看与您使用版本对应的文档。
