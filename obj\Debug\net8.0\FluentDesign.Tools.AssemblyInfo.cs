//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("FluentDesign WPF Team")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright © FluentDesign WPF Team 2024")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("A modern WPF control library based on Fluent Design System")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0-preview")]
[assembly: System.Reflection.AssemblyProductAttribute("FluentDesign WPF Controls")]
[assembly: System.Reflection.AssemblyTitleAttribute("FluentDesign Tools")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/FluentDesign/FluentDesign.WPF")]

// 由 MSBuild WriteCodeFragment 类生成。

