<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <AssemblyTitle>FluentDesign WPF Stress Tests</AssemblyTitle>
    <AssemblyDescription>Performance and stress tests for FluentDesign WPF Controls</AssemblyDescription>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.9.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.9.3" />
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="BenchmarkDotNet" Version="0.15.2" />
    <PackageReference Include="FluentAssertions" Version="8.3.0" />
    <PackageReference Include="NBomber" Version="6.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\FluentDesign.WPF\FluentDesign.WPF.csproj" />
    <ProjectReference Include="..\..\src\FluentDesign.Shared\FluentDesign.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Performance\" />
    <Folder Include="Memory\" />
    <Folder Include="Rendering\" />
    <Folder Include="DataBinding\" />
    <Folder Include="Reports\" />
  </ItemGroup>

</Project>
