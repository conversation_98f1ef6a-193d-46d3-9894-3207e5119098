<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <IsPackable>false</IsPackable>
    <AssemblyTitle>FluentDesign Shared</AssemblyTitle>
    <AssemblyDescription>Shared code and resources for FluentDesign WPF projects</AssemblyDescription>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Constants\" />
    <Folder Include="Helpers\" />
    <Folder Include="Extensions\" />
    <Folder Include="Interfaces\" />
    <Folder Include="Models\" />
    <Folder Include="Resources\" />
  </ItemGroup>

</Project>
